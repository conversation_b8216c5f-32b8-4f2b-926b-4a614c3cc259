<!DOCTYPE html>
<html>
<head>
    <title>Test API</title>
</head>
<body>
    <h1>Test des API de réapprovisionnement</h1>
    
    <button onclick="testCategories()">Tester API Catégories</button>
    <button onclick="testAllItems()">Tester API Articles</button>
    
    <div id="results"></div>

    <script>
        function testCategories() {
            fetch('/inventory/stock-replenishment/api/categories')
                .then(response => {
                    console.log('Status catégories:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Catégories:', data);
                    document.getElementById('results').innerHTML = '<h3>Catégories:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                })
                .catch(error => {
                    console.error('Erreur catégories:', error);
                    document.getElementById('results').innerHTML = '<h3>Erreur catégories:</h3><pre>' + error + '</pre>';
                });
        }

        function testAllItems() {
            fetch('/inventory/stock-replenishment/api/all-items')
                .then(response => {
                    console.log('Status articles:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Articles:', data);
                    document.getElementById('results').innerHTML = '<h3>Articles (' + data.length + '):</h3><pre>' + JSON.stringify(data.slice(0, 5), null, 2) + '</pre>';
                })
                .catch(error => {
                    console.error('Erreur articles:', error);
                    document.getElementById('results').innerHTML = '<h3>Erreur articles:</h3><pre>' + error + '</pre>';
                });
        }
    </script>
</body>
</html>
